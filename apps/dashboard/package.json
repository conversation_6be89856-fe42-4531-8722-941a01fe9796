{"dependencies": {"@mass/components": "workspace:*", "@mass/icons": "workspace:*", "@mass/tailwind": "workspace:*", "@mass/utils": "workspace:*", "@mass/api": "workspace:*", "react-slick": "^0.30.3", "slick-carousel": "^1.8.1", "i18next": "^25.3.2", "i18next-browser-languagedetector": "^8.2.0", "i18next-http-backend": "^3.0.2", "react-i18next": "^15.6.0", "@tailwindcss/vite": "^4.1.11", "@tanstack/react-router": "^1.127.3", "@tanstack/react-router-devtools": "^1.127.3", "@tanstack/router-plugin": "^1.127.5", "@legendapp/state": "^3.0.0-beta.31", "@radix-ui/react-dialog": "^1.1.14", "react": "^19.1.0", "react-dom": "^19.1.0", "tailwindcss": "^4.1.11"}, "devDependencies": {"serve": "^14.2.4", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react-swc": "^3.10.2", "globals": "^16.3.0", "typescript": "~5.8.3", "vite": "^7.0.4"}, "name": "dashboard", "private": true, "type": "module", "version": "0.0.0"}