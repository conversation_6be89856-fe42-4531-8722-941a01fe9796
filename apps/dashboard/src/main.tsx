import '@mass/tailwind/dashboard.css'
import '@mass/utils'

import { global$ } from '@mass/api'
import { createRouter, RouterProvider } from '@tanstack/react-router'
import { StrictMode } from 'react'
import { createRoot } from 'react-dom/client'
import { routeTree } from './routeTree.gen'

// Router
declare module '@tanstack/react-router' {
  interface Register {
    router: typeof router
  }
}

const router = createRouter<typeof routeTree>({
  context: global$,
  routeTree,
})

// biome-ignore lint/style/noNonNullAssertion: Redundant
createRoot(document.getElementById('root')!).render(
  <StrictMode>
    <RouterProvider router={router} />
  </StrictMode>,
)
