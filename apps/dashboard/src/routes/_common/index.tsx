import { use$ } from '@legendapp/state/react'
import { global$ } from '@mass/api'
import { ui$ } from '@mass/components/shared'
import { createFileRoute } from '@tanstack/react-router'
import { useTranslation } from 'react-i18next'

function Home() {
  const { t: dashboard } = useTranslation('dashboard')
  const user = use$(global$.user)

  return (
    <div className='flex flex-col p-2'>
      <h3> {dashboard('about-app')} </h3>
      <span>user: {(!!user).toString()}</span>

      <button
        type='button'
        onClick={() => {
          ui$.onChangeModal('document', true)
        }}>
        hi
      </button>
    </div>
  )
}

export const Route = createFileRoute('/_common/')({
  component: Home,
})
