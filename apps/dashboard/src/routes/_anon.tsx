import { global$ } from '@mass/api'
import { AuthLayout } from '@mass/components/dashboard'
import { createFileRoute, Outlet, redirect } from '@tanstack/react-router'

function RouteComponent() {
  return (
    <AuthLayout>
      <Outlet />
    </AuthLayout>
  )
}

export const Route = createFileRoute('/_anon')({
  beforeLoad() {
    const user = global$.user.get()

    if (user && user.type === 'end') {
      throw redirect({ to: '/' })
    }
  },

  component: RouteComponent,
})
