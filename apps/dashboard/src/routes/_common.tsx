import { global$ } from '@mass/api'
import { DocumentModal, TestModal } from '@mass/components/dashboard'
import { createFileRoute, Outlet, redirect } from '@tanstack/react-router'

function RouteComponent() {
  return (
    <div>
      <DocumentModal />
      <TestModal />
      <Outlet />
    </div>
  )
}

export const Route = createFileRoute('/_common')({
  beforeLoad() {
    const user = global$.user.get()
    if (!user || user.type === 'anon') {
      throw redirect({ to: '/login' })
    }
  },

  component: RouteComponent,
})
