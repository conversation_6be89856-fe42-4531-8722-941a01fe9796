import { authApi, global$, settingApi } from '@mass/api'
import { detector } from '@mass/utils'
import { createRootRouteWithContext, Outlet } from '@tanstack/react-router'
import { TanStackRouterDevtools } from '@tanstack/react-router-devtools'

export const Route = createRootRouteWithContext()({
  component: () => (
    <>
      <Outlet />
      <TanStackRouterDevtools />
    </>
  ),

  async beforeLoad() {
    try {
      // If user is already logged in, return
      if (global$.isUserValid()) {
        return
      }

      // Check if user is logged in (cookie)
      global$.user.set(await authApi.me())

      // If user is not logged in, return
      if (!global$.isUserValid()) {
        return
      }

      // Get user settings
      const [kvkk, userAgreement, language] = await Promise.all([
        settingApi.user({
          query: {
            key: 'agreements.kvkk',
          },
        }),
        settingApi.user({
          query: {
            key: 'agreements.user-agreement',
          },
        }),
        settingApi.user({
          query: {
            key: 'lang',
          },
        }),
      ])

      global$.aggreements.set({
        kvkk: kvkk.value === 'true',
        userAgreement: userAgreement.value === 'true',
      })

      const rawDetectedLanguage = detector.detect()
      const detectedLanguage = (
        (typeof rawDetectedLanguage === 'string' ? rawDetectedLanguage : rawDetectedLanguage?.at(0)) ?? ''
      ).split('-')[0]
      const targetLanguage = (language.value ?? detectedLanguage ?? 'tr') as 'tr' | 'en'

      await settingApi.changeLangauge(targetLanguage, language.value)

      // biome-ignore lint/suspicious/noEmptyBlockStatements: Redundant
    } catch {}
  },
})
