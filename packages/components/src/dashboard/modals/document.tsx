import { i18n } from '@mass/utils'
import type { FC } from 'react'
import { useTranslation } from 'react-i18next'

import { But<PERSON>, DialogContent, DialogHeader, DialogOverlay, DialogPortal, DialogRoot, ui$ } from '../../shared'

export const DocumentModal: FC = () => {
  const { t: dashboard } = useTranslation('dashboard')

  return (
    <DialogRoot name='document'>
      <DialogPortal>
        <DialogOverlay />
        <DialogContent>
          <DialogHeader
            sticky
            title={dashboard('user-agreement')}
            description={dashboard('aggree-to', {
              fileName: dashboard('user-agreement'),
              suffix: i18n.resolvedLanguage === 'tr' ? "'ni" : '',
            })}
          />

          <Button
            onClick={() => {
              ui$.onChangeModal('test', true)
            }}>
            Test Modal
          </Button>

          <div>
            {' '}
            Lorem ipsum dolor, sit amet consectetur adipisicing elit. Dolore earum nostrum voluptas expedita non
            consectetur vel voluptate aut ex ut corporis, quaerat ab minima beatae mollitia odit debitis voluptatum
            iste.{' '}
          </div>

          <div>
            {' '}
            Lorem ipsum dolor, sit amet consectetur adipisicing elit. Dolore earum nostrum voluptas expedita non
            consectetur vel voluptate aut ex ut corporis, quaerat ab minima beatae mollitia odit debitis voluptatum
            iste.{' '}
          </div>

          <div>
            {' '}
            Lorem ipsum dolor, sit amet consectetur adipisicing elit. Dolore earum nostrum voluptas expedita non
            consectetur vel voluptate aut ex ut corporis, quaerat ab minima beatae mollitia odit debitis voluptatum
            iste.{' '}
          </div>

          <div>
            {' '}
            Lorem ipsum dolor, sit amet consectetur adipisicing elit. Dolore earum nostrum voluptas expedita non
            consectetur vel voluptate aut ex ut corporis, quaerat ab minima beatae mollitia odit debitis voluptatum
            iste.{' '}
          </div>

          <div>
            {' '}
            Lorem ipsum dolor, sit amet consectetur adipisicing elit. Dolore earum nostrum voluptas expedita non
            consectetur vel voluptate aut ex ut corporis, quaerat ab minima beatae mollitia odit debitis voluptatum
            iste.{' '}
          </div>

          <div>
            {' '}
            Lorem ipsum dolor, sit amet consectetur adipisicing elit. Dolore earum nostrum voluptas expedita non
            consectetur vel voluptate aut ex ut corporis, quaerat ab minima beatae mollitia odit debitis voluptatum
            iste.{' '}
          </div>
        </DialogContent>
      </DialogPortal>
    </DialogRoot>
  )
}
