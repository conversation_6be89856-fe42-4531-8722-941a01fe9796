import clsx from 'clsx'
import type { FC } from 'react'

export interface ButtonStylesProps {
  variant?: 'custom' | undefined
  className?: string | undefined
}

// biome-ignore lint/style/useComponentExportOnlyModules: Redundant
export const useButtonStyles = ({ className, variant }: ButtonStylesProps) =>
  clsx(
    'flex items-center justify-center gap-6',
    'cursor-pointer rounded-b1', // styling
    'outline-primary',
    'text-black text-xs', // text
    'transition-colors duration-200', // animation
    {
      'w-full px-8 py-5': variant !== 'custom',
    }, // variants
    className,
  )

export const Button: FC<
  React.ButtonHTMLAttributes<HTMLButtonElement> &
    ButtonStylesProps & {
      children: React.ReactNode
    }
> = ({ className, variant, children, ...props }) => {
  const styles = useButtonStyles({ variant, className })

  return (
    <button type='button' className={styles} {...props}>
      {children}
    </button>
  )
}
