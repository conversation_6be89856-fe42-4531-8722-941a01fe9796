import clsx from 'clsx'
import type { FC } from 'react'

export interface TextStylesProps {
  variant?: 'dim' | 'dimmer' | 'subtitle' | 'slide' | 'dim-2'
  className?: string | undefined
}

// biome-ignore lint/style/useComponentExportOnlyModules: Redundant
export const useTextStyles = ({ variant, className }: TextStylesProps) =>
  clsx(
    {
      'text-dim-1 text-xs': variant === 'subtitle',
      'text-2xs text-dim-1': variant === 'dim',
      'text-2xs text-dim-1/80': variant === 'dimmer',
      'text-dim-2 text-xs': variant === 'dim-2',
      'font-urbanist text-2xl text-white sm:text-3xl xl:text-4xl': variant === 'slide',
    },
    className,
  )

export const Text: FC<{
  el?: 'p' | 'span'
  variant?: 'dim' | 'dimmer' | 'subtitle' | 'slide'
  className?: string
  children: React.ReactNode
}> = ({ el = 'p', variant = 'dim', className, children, ...props }) => {
  const El = el
  const styles = useTextStyles({ variant, className })

  return (
    <El className={styles} {...props}>
      {children}
    </El>
  )
}
