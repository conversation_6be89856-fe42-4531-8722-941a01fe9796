import { Link as $Link, type LinkComponentProps } from '@tanstack/react-router'
import clsx from 'clsx'
import type { FC } from 'react'

export interface LinkStylesProps {
  variant?: 'colored' | undefined
  className?: string | undefined
}

// biome-ignore lint/style/useComponentExportOnlyModules: Redundant
export const useLinkStyles = ({ variant, className }: LinkStylesProps) =>
  clsx(
    'cursor-pointer', // styling
    {
      'text-primary underline': variant === 'colored',
    },
    className,
  )

export const Link: FC<
  LinkComponentProps &
    LinkStylesProps & {
      to?: LinkComponentProps['to']
      children?: React.ReactNode
    }
> = ({ to, children, variant, className, ...props }) => {
  const styles = useLinkStyles({ variant, className })

  return (
    <$Link to={to} className={styles} {...props}>
      {children}
    </$Link>
  )
}
