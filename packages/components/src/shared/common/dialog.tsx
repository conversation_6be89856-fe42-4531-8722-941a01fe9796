import { use$ } from '@legendapp/state/react'
import { XClose } from '@mass/icons'
import {
  Close,
  Content,
  Description,
  type DialogCloseProps,
  type DialogContentProps,
  type DialogOverlayProps,
  type DialogProps,
  Overlay,
  Portal,
  Root,
  Title,
} from '@radix-ui/react-dialog'
import clsx from 'clsx'
import { createContext, type FC, use } from 'react'

import { ui$ } from '../stores/ui'
import { useButtonStyles } from './button'
import { useTextStyles } from './text'
import { useTitleStyles } from './title'

// biome-ignore lint/style/useComponentExportOnlyModules: Redundant
export const dialogContext = createContext({ name: null as unknown as Shared.Modals })
// biome-ignore lint/style/useComponentExportOnlyModules: Redundant
export const useDialogContext = () => use(dialogContext)

export const DialogRoot: FC<DialogProps & { name: Shared.Modals }> = ({ name, ...props }) => {
  const isModalOpen = use$(() => ui$.isModalOpen(name))

  return (
    <dialogContext.Provider value={{ name }}>
      <Root {...props} open={isModalOpen} onOpenChange={open => ui$.onChangeModal(name, open)} />
    </dialogContext.Provider>
  )
}

export const DialogOverlay: FC<DialogOverlayProps> = ({ className, style = {}, ...props }) => {
  const { name } = useDialogContext()
  const indexOfTargetModal = use$(() => (ui$.modal.get() || []).indexOf(name) + 1)
  const isLast = use$(() => indexOfTargetModal === (ui$.modal.get() || []).length)
  const zIndex = Math.max(indexOfTargetModal * 100, 100) + 1

  // TODO: animation

  return (
    <Overlay
      className={clsx(
        'fixed inset-0', // positioning
        {
          'bg-black/20 backdrop-blur-sm': isLast,
        },
        'data-[state=open]:animate-fade-in', // animate
        className,
      )}
      style={{
        ...style,

        zIndex,
      }}
      {...props}
    />
  )
}

export const DialogContent: FC<DialogContentProps & { ref?: React.Ref<HTMLDivElement> }> = ({
  ref,
  className,
  style = {},
  children,
  ...props
}) => {
  const { name } = useDialogContext()
  const indexOfTargetModal = use$(() => (ui$.modal.get() || []).indexOf(name) + 1)
  const zIndex = Math.max(indexOfTargetModal * 100, 100) + 1

  return (
    <Content
      ref={ref}
      className={clsx(
        'fixed inset-0', // positioning (common)
        'max-sm:top-20 max-sm:max-h-[calc(100vh-2.5rem)]', // positioning (mobile)
        'sm:-translate-x-1/2 sm:-translate-y-1/2 sm:top-1/2 sm:left-1/2 sm:max-h-screen', // positioning (desktop)
        'flex flex-col px-8 pb-8', // centering & padding
        'bg-white outline-none sm:rounded-b2', // styling
        'data-[state=open]:animate-fade-in-scale-to-top', // animate
        'scrollbar-b2 overflow-y-auto overflow-x-hidden', // scroll
        className,
      )}
      style={{
        zIndex,
        ...style,
      }}
      {...props}>
      {children}
    </Content>
  )
}

export const DialogHeader: FC<
  React.HTMLAttributes<HTMLDivElement> & { sticky?: boolean; title?: string; description?: string }
> = ({ sticky = false, className, title, description, children, ...props }) => {
  const titleStyles = useTitleStyles({
    variant: 'h4',
  })

  const descriptionStyles = useTextStyles({
    variant: 'dim-2',
  })

  return (
    <section
      className={clsx(
        'flex flex-col gap-0',
        '-mx-8 w-[calc(100%+2rem)] px-8 pt-8 pb-8', // sizing
        'border-accessory-1 border-b', // accessory
        {
          'sticky top-0 bg-white': sticky,
        },
        className,
      )}
      {...props}>
      <Title className={titleStyles}> {title} </Title>
      <Description className={descriptionStyles}> {description} </Description>
      {children}

      <DialogClose position='top-right'> as </DialogClose>
    </section>
  )
}

export const DialogClose: FC<DialogCloseProps & { position?: 'none' | 'top-right' }> = ({
  position = 'none',
  className,
  ...props
}) => {
  const buttonStyles = useButtonStyles({
    variant: 'custom',
    className: clsx(
      'p-2',
      {
        'absolute top-8 right-8': position === 'top-right',
      },
      className,
    ),
  })

  return (
    <Close className={buttonStyles} {...props}>
      <XClose className='h-10 w-10' />
    </Close>
  )
}
// biome-ignore lint/style/useComponentExportOnlyModules: Redundant
export const DialogPortal = Portal
