import type { FC, SVGProps } from 'react'

export const LogoFull: FC<SVGProps<SVGSVGElement>> = props => (
  <svg xmlns='http://www.w3.org/2000/svg' width='144' height='48' viewBox='0 0 144 48' fill='none' {...props}>
    <title>MASS full logo</title>
    <g filter='url(#a)'>
      <g clipPath='url(#b)'>
        <rect width={48} height={48} fill='#2E90FA' rx={12} />
        <path fill='url(#c)' d='M0 0h48v48H0z' />
        <path fill='#84CAFF' d='M14.026 11h4.026v10.588l-4.026 5.323H10V16.324L14.026 11Z' />
        <path fill='#B2DDFF' d='m18.052 11-4.026 5.323v10.588l4.026-5.323V11Z' />
        <path fill='#53B1FD' d='M14.026 16.323H10L14.026 11h4.026l-4.026 5.323Z' />
        <path fill='#84CAFF' d='M23.922 11h4.026v17.298l-4.026 5.347h-4.026V16.323L23.922 11Z' />
        <path fill='#B2DDFF' d='m27.948 11-4.025 5.323v17.238l4.025-5.254V11Z' />
        <path fill='#53B1FD' d='M23.922 16.323h-4.026L23.922 11h4.026l-4.026 5.323Z' />
        <path fill='#84CAFF' d='M33.82 11h4.025v20.653L33.82 37h-4.025V16.323L33.819 11Z' />
        <path fill='#53B1FD' d='M33.82 16.323h-4.026L33.819 11h4.026l-4.026 5.323Z' />
        <path fill='#B2DDFF' d='M37.845 31.662V11l-4.026 5.322V37l4.026-5.338Z' />
      </g>
      <rect width={46} height={46} x={1} y={1} stroke='url(#d)' strokeWidth={2} rx={11} />
    </g>
    <path
      fill='#000'
      d='M81.904 14.259c3.933 0 7.12 3.181 7.12 7.107V33.7h-3.492V21.366a3.629 3.629 0 0 0-3.628-3.621c-2 0-3.662 1.624-3.662 3.62V33.7h-3.493V21.366a3.629 3.629 0 0 0-3.628-3.621c-2 0-3.662 1.624-3.662 3.62V33.7H64V14.25h3.459v1.023a7.175 7.175 0 0 1 3.662-1.015c2.136 0 4.069.947 5.391 2.47 1.323-1.522 3.222-2.47 5.392-2.47ZM104.341 14.32h3.493v19.429h-3.493v-1.127c-.983.478-2.068.785-3.221.785h-2c-4.34 0-7.867-3.585-7.867-7.956v-3.278c0-4.37 3.526-7.922 7.867-7.922h2.305c1.018 0 2.035.24 2.916.649v-.58Zm0 13.521v-8.297a3.3 3.3 0 0 0-2.916-1.776H99.12c-2.408 0-4.408 1.98-4.408 4.405v3.278c0 2.459 2 4.44 4.408 4.44h2c1.39 0 2.611-.786 3.221-2.05ZM124.137 26.083c.574 2.035-.101 4.443-1.621 5.834-1.486 1.357-3.445 1.832-5.471 1.832-2.263 0-4.626-.61-6.653-1.323l1.148-3.29c4.053 1.424 7.194 1.492 8.612.17.574-.51.844-1.527.642-2.308-.169-.678-.574-.814-.878-.882a17.202 17.202 0 0 0-1.858-.27c-2.465-.306-5.572-.68-7.159-2.884-1.148-1.595-1.115-3.8.101-5.8 1.79-2.952 6.754-3.732 12.968-2.002l-.912 3.358c-4.896-1.357-8.341-.78-9.084.475-.338.577-.608 1.425-.236 1.933.675.984 3.039 1.256 4.728 1.46.81.1 1.519.169 2.195.304 1.756.373 3.005 1.629 3.478 3.393ZM140.795 26.083c.58 2.035-.103 4.443-1.639 5.834-1.502 1.357-3.481 1.832-5.529 1.832-2.287 0-4.677-.61-6.725-1.323l1.161-3.29c4.096 1.424 7.27 1.492 8.704.17.58-.51.853-1.527.648-2.308-.17-.678-.58-.814-.887-.882a17.598 17.598 0 0 0-1.877-.27c-2.492-.306-5.633-.68-7.237-2.884-1.16-1.595-1.126-3.8.103-5.8 1.809-2.952 6.826-3.732 13.107-2.002l-.922 3.358c-4.949-1.357-8.431-.78-9.182.475-.341.577-.614 1.425-.239 1.933.683.984 3.072 1.256 4.779 1.46.819.1 1.536.169 2.219.304 1.775.373 3.038 1.629 3.516 3.393Z'
    />
    <defs>
      <linearGradient id='c' x1={24} x2={26} y1={0} y2={48} gradientUnits='userSpaceOnUse'>
        <stop stopColor='#fff' stopOpacity={0} />
        <stop offset={1} stopColor='#fff' stopOpacity={0.12} />
      </linearGradient>
      <linearGradient id='d' x1={24} x2={24} y1={0} y2={48} gradientUnits='userSpaceOnUse'>
        <stop stopColor='#fff' stopOpacity={0.12} />
        <stop offset={1} stopColor='#fff' stopOpacity={0} />
      </linearGradient>
      <clipPath id='b'>
        <rect width={48} height={48} fill='#fff' rx={12} />
      </clipPath>
      <filter id='a' width={48} height={51} x={0} y={-3} colorInterpolationFilters='sRGB' filterUnits='userSpaceOnUse'>
        <feFlood floodOpacity={0} result='BackgroundImageFix' />
        <feBlend in='SourceGraphic' in2='BackgroundImageFix' result='shape' />
        <feColorMatrix in='SourceAlpha' result='hardAlpha' values='0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0' />
        <feOffset dy={-3} />
        <feGaussianBlur stdDeviation={1.5} />
        <feComposite in2='hardAlpha' k2={-1} k3={1} operator='arithmetic' />
        <feColorMatrix values='0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0' />
        <feBlend in2='shape' result='effect1_innerShadow_1_31' />
      </filter>
    </defs>
  </svg>
)
