import type { BlobType, Merge } from '@mass/utils'
import { type } from 'arktype'

export const apiBuidler = <const D extends Api.Definition>(
  definition: D,
  defaultPayload: Api.GetPayload<D> = {} as Api.GetPayload<D>,
) => {
  const targetApi = Object.assign(
    {
      method: 'GET',
      url: '/test',

      // data
      query: type({}),
      params: type({}),
      payload: type({}),
      response: type({}),
    },
    definition,
  ) as unknown as Merge<Required<Api.Definition>, Required<D>>

  return async (
    options: { payload?: Api.GetPayload<D>; query?: Api.GetQuery<D>; params?: Api.GetParams<D> } = {},
  ): Promise<Api.GetResponse<D>> => {
    options.params = (options.params ?? {}) as BlobType
    options.query = (options.query ?? {}) as BlobType
    options.payload = (options.payload ?? {}) as BlobType

    const payload = Object.assign({}, defaultPayload, options.payload)
    const payloadOut = targetApi.payload(payload)
    const queryOut = targetApi.query(options.query)
    const paramsOut = targetApi.params(options.params)

    if (payloadOut instanceof type.errors) {
      console.error(`Api: ${name} - Invalid payload`, payloadOut)
    } else if (queryOut instanceof type.errors) {
      console.error(`Api: ${name} - Invalid query`, queryOut)
    } else if (paramsOut instanceof type.errors) {
      console.error(`Api: ${name} - Invalid params`, paramsOut)
    }

    try {
      let url = `/api${targetApi.url}`

      const queryEntries = Object.entries(options.query ?? {})

      if (queryEntries.length > 0) {
        for (const [key, value] of queryEntries) {
          url = url.replace(`$${key}`, value as BlobType)
        }
      }

      const requestOptions = {
        method: targetApi.method,
        credentials: 'include',
      } as RequestInit

      if ((targetApi.method as string) !== 'GET') {
        requestOptions.body = JSON.stringify(payload)
      }

      const response = await fetch(url, requestOptions)

      if (!response.ok) {
        throw new Error(`Api: ${name} - ${response.status} ${response.statusText}`)
      }

      const responseData = await response.json()

      return responseData as Api.GetResponse<D>
    } catch (err) {
      console.error(`Api: ${name}`, { name, payload, targetApi })
      console.error(err)

      throw err
    }
  }
}
