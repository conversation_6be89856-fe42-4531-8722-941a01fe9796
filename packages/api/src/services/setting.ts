import { i18n } from '@mass/utils'
import { type } from 'arktype'
import { global$ } from '../stores/global'
import { apiBuidler } from '../utils/api-builder'

export const settingApi = {
  user: apiBuidler({
    url: '/setting/user/$key',

    // data
    query: type({
      key: 'string',
    }),

    response: type({
      value: 'string',
    }),
  }),

  updateUser: apiBuidler({
    method: 'PATCH',
    url: '/setting/user/$key',

    // data
    query: type({
      key: 'string',
    }),
    payload: type({
      value: 'string',
    }),
    response: type('unknown'),
  }),

  changeLangauge: async (lang: 'tr' | 'en', currentLang: string | null) => {
    if (global$.isUserValid() && currentLang !== lang) {
      await settingApi.updateUser({
        query: {
          key: 'lang',
        },
        payload: {
          value: lang,
        },
      })
    }

    global$.language.set(lang)
    i18n.changeLanguage(lang)
    document.querySelector('html')?.setAttribute('lang', lang)
  },
} as const
