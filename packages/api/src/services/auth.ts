import { type } from 'arktype'

import { apiBuidler } from '../utils/api-builder'

const BASE_USER_TYPE = type({
  id: 'string',
  permissions: type({
    scope: 'string',
  }).array(),
})

export const authApi = {
  me: apiBuidler({
    url: '/auth/me',

    // data
    response: type(
      BASE_USER_TYPE.merge({
        type: "'anon'",
      }),
    ).or(
      BASE_USER_TYPE.merge({
        type: "'end'",
        email: 'string | null',
        phone: 'string | null',
        tckn: 'string',
        firstName: 'string',
        lastName: 'string',
      }),
    ),
  }),

  login: {
    end: () => {
      const url = '/api/auth/edevlet'
      window.open(url, '_self')
    },
  },

} as const
