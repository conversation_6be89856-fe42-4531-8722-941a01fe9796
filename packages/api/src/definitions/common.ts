import type { BlobType, EmptyType, Fn } from '@mass/utils'
import type { Type } from 'arktype'

import type { authApi } from '../services/auth'

declare global {
  namespace Api {
    interface Definition {
      method?: 'GET' | 'POST' | 'PATCH' | 'DELETE' | 'window.open'
      url: string

      query?: Type<object, EmptyType>
      params?: Type<object, EmptyType>
      payload?: Type<object, EmptyType>
      response?: Type<unknown>
    }

    type GetUrl<D extends Api.Definition> = D['url']
    type GetMethod<D extends Api.Definition> = D['method']
    type GetPayload<D extends Api.Definition> = D['payload'] extends Type<infer T> ? T : EmptyType
    type GetQuery<D extends Api.Definition> = D['query'] extends Type<infer T> ? T : EmptyType
    type GetParams<D extends Api.Definition> = D['params'] extends Type<infer T> ? T : EmptyType
    type GetResponse<D extends Api.Definition> = D['response'] extends Type<infer T> ? T : EmptyType

    type ExtractResponse<T> = T extends Fn<BlobType[], infer R> ? Awaited<R> : T

    interface Services {
      auth: typeof authApi
    }
  }
}
