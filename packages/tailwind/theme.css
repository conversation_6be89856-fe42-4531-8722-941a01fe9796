@theme {
  --*: initial;

  --spacing: 0.125rem;

  --breakpoint-xs: 26.25rem;
  --breakpoint-sm: 40rem;
  --breakpoint-md: 48rem;
  --breakpoint-lg: 64rem;
  --breakpoint-xl: 80rem;
  --breakpoint-2xl: 96rem;

  --blur-xs: 4px;
  --blur-sm: 8px;
  --blur-md: 12px;
  --blur-lg: 16px;
  --blur-xl: 24px;
  --blur-2xl: 40px;
  --blur-3xl: 64px;

  --aspect-video: 16 / 9;

  --font-base: "Inter", ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji",
    "Segoe UI Emoji", Segoe UI Symbol, "Noto Color Emoji";
  --font-urbanist: "Urbanist", ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji",
    "Segoe UI Emoji", Segoe UI Symbol, "Noto Color Emoji"; 

  --font-weight-thin: 100;
  --font-weight-extralight: 200;
  --font-weight-light: 300;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  --font-weight-extrabold: 800;
  --font-weight-black: 900;

  --radius-full: 100%;
  --radius-max: 2.097rem; /* 33554400px max css value */
  --radius-b1: 0.625rem; /* 10px */
  --radius-b2: 0.75rem; /* 12px */
  --radius-c1: 1rem; /* 16px */

  --text-2xs: .75rem;
  --text-2xs--line-height: calc(1 / 0.75);
  --text-xs: .875rem;
  --text-xs--line-height: calc(1.25 / 0.875);
  --text-base: 1rem;
  --text-base--line-height: calc(1.5 / 1);
  --text-md: 1.125rem;
  --text-md--line-height: calc(1.75 / 1.125);

  --text-2xl: 1.5rem;
  --text-2xl--line-height: 2rem;
  --text-3xl: 1.875rem;
  --text-3xl--line-height: 2.25rem;
  --text-4xl: 2.25rem;
  --text-4xl--line-height: 2.5rem;

  --color-white: oklch(1 0 0); /* #ffffff */
  --color-black: oklch(0 0 0); /* #000000 */
 
  --color-primary: oklch(0.6518 0.1809 253.92); /* #2e90fa */

  --color-dim-1: oklch(0.3938 0.0197 266.05); /* #414651 */  
  --color-dim-2: oklch(0.4596 0.0174 264.39); /* #535862 */
  --color-title-1: oklch(0.2886 0.0236 264.1); /* #252b37 */
  --color-title-2: oklch(0.1469 0.0041 49.25); /* #0c0a09 */

  --color-accessory-1: oklch(0.9276 0.0058 264.53); /* #e5e7eb */

  --color-turkey: oklch(0.6024 0.23 25.49); /* #eb212e */

  --color-transparent: transparent;

  --animate-fade-in: fade-in 0.2s ease-out;
  @keyframes fade-in {
    0% {
      opacity: 0;
    }
    100% {
      opacity: 1;
    }
  }

  --animate-fade-in-scale-to-top: fade-in-scale-to-top 0.3s ease-out;
  @keyframes fade-in-scale-to-top {
    0% {
      opacity: 0;
      transform: scale(0.9) translate3d(0, 10%, 0);
    }
    100% {
      opacity: 1;
      transform: scale(1) translate3d(0, 0, 0);
    }
  }
}
